#!/usr/bin/env python3
"""
ExpManager服务启动脚本
自动启动后端和前端服务
"""

import os
import sys
import time
import subprocess
import webbrowser
from pathlib import Path


def check_port_available(port):
    """检查端口是否可用"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('localhost', port))
            return result != 0
    except:
        return True


def start_backend():
    """启动后端服务"""
    print("启动后端服务...")

    backend_path = Path("backend")
    if not backend_path.exists():
        print("错误: 后端目录不存在")
        return None

    # 检查端口8000
    if not check_port_available(8000):
        print("后端服务已在运行 (端口8000)")
        return None

    try:
        # 启动后端
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", "main:app",
            "--host", "0.0.0.0", "--port", "8000", "--reload"
        ], cwd=backend_path)

        print("后端服务启动成功 (http://localhost:8000)")
        return process

    except Exception as e:
        print(f"后端启动失败: {e}")
        return None


def start_frontend():
    """启动前端服务"""
    print("启动前端服务...")

    frontend_path = Path("frontend")
    if not frontend_path.exists():
        print("错误: 前端目录不存在")
        return None

    # 检查端口3000
    if not check_port_available(3000):
        print("前端服务已在运行 (端口3000)")
        return None

    # 检查Node.js
    try:
        subprocess.check_call(["node", "--version"],
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    except:
        print("错误: Node.js未安装，无法启动前端")
        print("请安装Node.js: https://nodejs.org/")
        return None

    # 检查npm
    try:
        subprocess.check_call(["npm", "--version"],
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    except:
        print("错误: npm未安装，无法启动前端")
        return None

    try:
        # 检查依赖
        if not (frontend_path / "node_modules").exists():
            print("安装前端依赖...")
            subprocess.check_call(["npm", "install"], cwd=frontend_path)

        # 启动前端
        process = subprocess.Popen([
            "npm", "run", "dev", "--", "--port", "3000"
        ], cwd=frontend_path)

        print("前端服务启动成功 (http://localhost:3000)")
        return process

    except Exception as e:
        print(f"前端启动失败: {e}")
        return None


def wait_for_services():
    """等待服务启动"""
    print("等待服务启动...")

    import requests

    # 等待后端
    for i in range(30):
        try:
            response = requests.get("http://localhost:8000/health", timeout=1)
            if response.status_code == 200:
                print("后端服务就绪")
                break
        except:
            pass
        time.sleep(1)
    else:
        print("警告: 后端服务启动超时")

    # 等待前端
    for i in range(30):
        try:
            response = requests.get("http://localhost:3000", timeout=1)
            if response.status_code in [200, 404]:  # 404也表示服务在运行
                print("前端服务就绪")
                break
        except:
            pass
        time.sleep(1)
    else:
        print("警告: 前端服务启动超时")


def open_browser():
    """打开浏览器"""
    print("打开浏览器...")
    try:
        webbrowser.open("http://localhost:3000")
        print("浏览器已打开")
    except:
        print("警告: 无法自动打开浏览器")
        print("请手动访问: http://localhost:3000")


def main():
    """主函数"""
    print("=" * 60)
    print("ExpManager服务启动器")
    print("=" * 60)
    
    # 检查当前目录
    if not Path("backend").exists() and not Path("frontend").exists():
        print("错误: 未找到backend或frontend目录")
        print("请在experiment-manager目录下运行此脚本")
        sys.exit(1)

    processes = []

    try:
        # 启动后端
        backend_process = start_backend()
        if backend_process:
            processes.append(backend_process)

        # 启动前端
        frontend_process = start_frontend()
        if frontend_process:
            processes.append(frontend_process)

        if not processes:
            print("警告: 没有启动任何新服务")
        else:
            # 等待服务启动
            wait_for_services()

            # 打开浏览器
            open_browser()

        print("\n" + "=" * 60)
        print("服务启动完成！")
        print("访问地址:")
        print("   前端界面: http://localhost:3000")
        print("   后端API: http://localhost:8000")
        print("   API文档: http://localhost:8000/docs")
        print("\n现在可以重新运行你的实验脚本")
        print("   复盘页面将正常显示")
        print("\n按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 保持运行
        if processes:
            try:
                while True:
                    time.sleep(1)
                    # 检查进程是否还在运行
                    for process in processes[:]:
                        if process.poll() is not None:
                            processes.remove(process)
                    
                    if not processes:
                        print("警告: 所有服务已停止")
                        break
            except KeyboardInterrupt:
                print("\n正在停止服务...")
                for process in processes:
                    try:
                        process.terminate()
                        process.wait(timeout=5)
                    except:
                        process.kill()
                print("服务已停止")

    except KeyboardInterrupt:
        print("\n警告: 启动被用户中断")
        for process in processes:
            try:
                process.terminate()
            except:
                pass
        sys.exit(1)
    except Exception as e:
        print(f"\n错误: 启动过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

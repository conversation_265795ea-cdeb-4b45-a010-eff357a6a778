"""
简单实验示例
演示如何在Python脚本中使用实验管理系统
"""

import sys
import os
import time
import random

# 添加父目录到路径，以便导入expmanager
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from expmanager import start_experiment


def run_machine_learning_experiment():
    """模拟一个机器学习实验"""
    
    # 核心：启动实验管理
    exp_id = start_experiment(
        name="学习率优化实验",
        hypothesis="降低学习率从0.01到0.001将提升模型收敛性和最终准确率"
    )

    print(f"开始执行实验 (ID: {exp_id})")

    # 模拟实验参数
    learning_rates = [0.01, 0.001]
    results = {}

    for lr in learning_rates:
        print(f"测试学习率: {lr}")
        
        # 模拟训练过程
        for epoch in range(1, 6):
            # 模拟训练时间
            time.sleep(0.5)
            
            # 模拟准确率提升
            accuracy = 0.5 + (epoch * 0.1) + random.uniform(-0.05, 0.05)
            if lr == 0.001:  # 较小学习率表现更好
                accuracy += 0.1
                
            print(f"  Epoch {epoch}: 准确率 = {accuracy:.3f}")
        
        # 记录最终结果
        final_accuracy = accuracy
        results[lr] = final_accuracy
        print(f"学习率 {lr} 最终准确率: {final_accuracy:.3f}")

    # 分析结果
    best_lr = max(results, key=results.get)
    print(f"\n实验结论:")
    print(f"   最佳学习率: {best_lr}")
    print(f"   最高准确率: {results[best_lr]:.3f}")

    if best_lr == 0.001:
        print("假设验证成功：较小的学习率确实提升了模型性能")
    else:
        print("假设验证失败：较大的学习率表现更好")

    print(f"\n实验完成！即将自动打开复盘页面...")
    
    # 脚本结束时会自动触发atexit钩子，打开复盘页面


if __name__ == "__main__":
    print("=" * 50)
    print("实验管理系统 - 使用示例")
    print("=" * 50)

    try:
        run_machine_learning_experiment()
    except KeyboardInterrupt:
        print("\n警告: 实验被用户中断")
    except Exception as e:
        print(f"\n错误: 实验执行出错: {e}")

    print("\n" + "=" * 50)

"""
ExpManager库使用示例
演示如何在Python项目中使用expmanager库
"""

import sys
import os
import time
import random

# 添加父目录到Python路径（用于本地开发）
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import numpy as np
    from sklearn.datasets import make_classification
    from sklearn.model_selection import train_test_split
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.linear_model import LogisticRegression
    from sklearn.metrics import accuracy_score, classification_report
    SKLEARN_AVAILABLE = True
except ImportError:
    print("警告: scikit-learn未安装，将使用模拟数据")
    SKLEARN_AVAILABLE = False

# 导入expmanager库
try:
    from expmanager import start_experiment, configure
    print("ExpManager库导入成功")
except ImportError as e:
    print(f"错误: ExpManager库导入失败: {e}")
    print("解决方案:")
    print("   1. 运行: pip install -e . （在experiment-manager目录下）")
    print("   2. 或者运行: pip install expmanager")
    sys.exit(1)


def setup_environment():
    """配置实验环境"""
    print("配置实验环境...")

    # 全局配置
    configure(
        verbose=True,                    # 详细输出
        auto_open_browser=True,          # 自动打开浏览器
        fallback_mode=True,              # 降级模式（服务器不可用时仍可运行）
        server_auto_start=True           # 自动启动服务器
    )

    print("环境配置完成")


def experiment_1_model_comparison():
    """实验1: 模型对比实验"""
    
    # 🔥 启动实验管理
    exp_id = start_experiment(
        name="随机森林 vs 逻辑回归对比实验",
        hypothesis="在合成数据集上，随机森林的准确率将比逻辑回归高5%以上",
        description="使用sklearn生成的合成分类数据集，对比两种经典算法的性能",
        tags=["model-comparison", "sklearn", "classification"]
    )
    
    print(f"🧪 实验1已启动 (ID: {exp_id})")
    
    # 生成合成数据
    print("📊 生成合成数据集...")
    X, y = make_classification(
        n_samples=1000,
        n_features=20,
        n_informative=15,
        n_redundant=5,
        n_classes=2,
        random_state=42
    )
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    results = {}
    
    # 测试随机森林
    print("🌲 训练随机森林...")
    rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
    rf_model.fit(X_train, y_train)
    rf_pred = rf_model.predict(X_test)
    rf_accuracy = accuracy_score(y_test, rf_pred)
    results["RandomForest"] = rf_accuracy
    
    print(f"   随机森林准确率: {rf_accuracy:.4f}")
    
    # 测试逻辑回归
    print("📈 训练逻辑回归...")
    lr_model = LogisticRegression(random_state=42, max_iter=1000)
    lr_model.fit(X_train, y_train)
    lr_pred = lr_model.predict(X_test)
    lr_accuracy = accuracy_score(y_test, lr_pred)
    results["LogisticRegression"] = lr_accuracy
    
    print(f"   逻辑回归准确率: {lr_accuracy:.4f}")
    
    # 分析结果
    best_model = max(results, key=results.get)
    accuracy_diff = abs(results["RandomForest"] - results["LogisticRegression"])
    
    print(f"\n🎯 实验结果:")
    print(f"   最佳模型: {best_model}")
    print(f"   准确率差异: {accuracy_diff:.4f}")
    
    if best_model == "RandomForest" and accuracy_diff >= 0.05:
        print("✅ 假设验证成功：随机森林确实比逻辑回归准确率高5%以上")
    else:
        print("❌ 假设验证失败：结果与预期不符")
    
    print(f"\n🔚 实验1完成！")
    # 脚本结束时会自动打开复盘页面


def experiment_2_hyperparameter_tuning():
    """实验2: 超参数调优实验"""
    
    # 🔥 启动实验管理
    exp_id = start_experiment(
        name="随机森林超参数调优实验",
        hypothesis="增加树的数量到200将显著提升模型性能",
        description="测试不同n_estimators参数对随机森林性能的影响",
        tags=["hyperparameter-tuning", "random-forest", "optimization"]
    )
    
    print(f"🧪 实验2已启动 (ID: {exp_id})")
    
    # 生成数据
    print("📊 准备数据...")
    X, y = make_classification(
        n_samples=1500,
        n_features=25,
        n_informative=20,
        n_classes=3,
        random_state=123
    )
    
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=123
    )
    
    # 测试不同的n_estimators
    n_estimators_list = [50, 100, 150, 200, 250]
    results = {}
    
    for n_est in n_estimators_list:
        print(f"🌲 测试 n_estimators={n_est}...")
        
        model = RandomForestClassifier(n_estimators=n_est, random_state=42)
        model.fit(X_train, y_train)
        pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, pred)
        results[n_est] = accuracy
        
        print(f"   准确率: {accuracy:.4f}")
        time.sleep(0.5)  # 模拟训练时间
    
    # 分析结果
    best_n_est = max(results, key=results.get)
    best_accuracy = results[best_n_est]
    baseline_accuracy = results[100]  # 基线
    
    print(f"\n🎯 实验结果:")
    print(f"   最佳参数: n_estimators={best_n_est}")
    print(f"   最佳准确率: {best_accuracy:.4f}")
    print(f"   基线准确率: {baseline_accuracy:.4f}")
    print(f"   性能提升: {best_accuracy - baseline_accuracy:.4f}")
    
    if best_n_est == 200 and (best_accuracy - baseline_accuracy) > 0.01:
        print("✅ 假设验证成功：n_estimators=200确实显著提升了性能")
    else:
        print("❌ 假设验证失败：结果与预期不符")
    
    print(f"\n🔚 实验2完成！")


def experiment_3_feature_importance():
    """实验3: 特征重要性分析实验"""
    
    # 🔥 启动实验管理
    exp_id = start_experiment(
        name="特征重要性分析实验",
        hypothesis="前5个最重要特征包含80%以上的预测信息",
        description="分析随机森林模型中各特征的重要性分布",
        tags=["feature-importance", "analysis", "interpretability"]
    )
    
    print(f"🧪 实验3已启动 (ID: {exp_id})")
    
    # 生成有明确特征重要性的数据
    print("📊 生成特征数据...")
    X, y = make_classification(
        n_samples=2000,
        n_features=30,
        n_informative=10,
        n_redundant=10,
        n_clusters_per_class=1,
        random_state=456
    )
    
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=456
    )
    
    # 训练模型
    print("🌲 训练随机森林模型...")
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    
    # 获取特征重要性
    feature_importance = model.feature_importances_
    
    # 排序特征重要性
    sorted_indices = np.argsort(feature_importance)[::-1]
    sorted_importance = feature_importance[sorted_indices]
    
    # 计算累积重要性
    cumulative_importance = np.cumsum(sorted_importance)
    
    # 找到前5个特征的累积重要性
    top5_importance = cumulative_importance[4]  # 索引4表示前5个
    
    print(f"\n📊 特征重要性分析:")
    print(f"   前5个特征重要性: {top5_importance:.4f}")
    print(f"   前10个特征重要性: {cumulative_importance[9]:.4f}")
    
    # 显示最重要的特征
    print(f"\n🔝 最重要的5个特征:")
    for i in range(5):
        feature_idx = sorted_indices[i]
        importance = sorted_importance[i]
        print(f"   特征{feature_idx}: {importance:.4f}")
    
    # 验证假设
    if top5_importance >= 0.8:
        print("✅ 假设验证成功：前5个特征确实包含80%以上的预测信息")
    else:
        print(f"❌ 假设验证失败：前5个特征仅包含{top5_importance:.1%}的预测信息")
    
    print(f"\n🔚 实验3完成！")


def main():
    """主函数 - 运行所有实验"""
    print("=" * 60)
    print("🚀 ExpManager库使用示例")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    try:
        # 运行实验1
        print("\n" + "=" * 40)
        experiment_1_model_comparison()
        
        # 短暂暂停
        time.sleep(2)
        
        # 运行实验2
        print("\n" + "=" * 40)
        experiment_2_hyperparameter_tuning()
        
        # 短暂暂停
        time.sleep(2)
        
        # 运行实验3
        print("\n" + "=" * 40)
        experiment_3_feature_importance()
        
    except KeyboardInterrupt:
        print("\n⚠️ 实验被用户中断")
    except Exception as e:
        print(f"\n❌ 实验执行出错: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 所有实验完成！")
    print("📊 请查看自动打开的复盘页面进行详细分析")
    print("=" * 60)


if __name__ == "__main__":
    main()

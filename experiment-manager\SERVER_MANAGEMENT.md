# ExpManager 服务器管理指南

## 🚀 自动启动功能

ExpManager Library 现在支持**完全自动化的服务器管理**：

### ✅ 自动启动
当你运行实验脚本时，ExpManager会自动：
1. 检查服务器状态
2. 如果服务器未运行，自动启动后端服务
3. 注册实验并设置复盘钩子

```python
from expmanager import start_experiment

# 🚀 这一行代码会自动启动服务器（如果需要）
exp_id = start_experiment(
    name="我的实验",
    hypothesis="我的假设"
)

# 你的实验代码...
print("实验进行中...")

# 脚本结束时自动打开复盘页面
```

### ✅ 持续运行
- 服务器启动后会**持续运行**，不会随脚本结束而停止
- 这样你可以运行多个实验脚本，共享同一个服务器实例
- 复盘页面始终可访问：http://localhost:3000

## 🛑 手动停止服务器

当你完成所有实验工作后，可以手动停止服务器：

### 方法1：使用停止脚本
```bash
python stop_server.py
```

### 方法2：使用任务管理器（Windows）
1. 打开任务管理器
2. 找到python.exe进程（监听端口8000的进程）
3. 结束进程

### 方法3：使用命令行（Windows）
```bash
# 查找监听8000端口的进程
netstat -ano | findstr :8000

# 停止进程（替换PID为实际进程ID）
taskkill /F /PID <PID>
```

### 方法4：使用命令行（Linux/Mac）
```bash
# 查找并停止监听8000端口的进程
lsof -ti:8000 | xargs kill -9
```

## 📊 服务器状态检查

### 检查服务器是否运行
```bash
# 方法1：检查端口
netstat -ano | findstr :8000

# 方法2：测试API
curl http://localhost:8000/health
```

### 访问服务
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs  
- **前端界面**: http://localhost:3000
- **复盘页面**: http://localhost:3000/experiments/{实验ID}/review

## ⚙️ 配置选项

你可以通过配置来控制自动启动行为：

```python
from expmanager import configure

configure(
    server_auto_start=True,      # 是否自动启动服务器
    verbose=True,                # 是否显示详细日志
    auto_open_browser=True,      # 是否自动打开浏览器
    fallback_mode=True           # 服务器不可用时是否降级运行
)
```

## 🔧 故障排除

### 问题1：端口被占用
如果看到"端口8000已被占用"的错误：
1. 运行 `python stop_server.py` 停止现有服务器
2. 或者重启计算机清理所有进程

### 问题2：服务器启动失败
如果自动启动失败：
1. 检查Python环境是否正确安装了依赖
2. 手动运行：`pip install -r backend/requirements.txt`
3. 检查防火墙是否阻止了端口8000

### 问题3：复盘页面无法访问
如果复盘页面显示"连接被拒绝"：
1. 确认后端服务器正在运行：`curl http://localhost:8000/health`
2. 如果后端正常但前端不可用，这是正常的（前端是可选的）
3. 你仍然可以通过API访问数据：`http://localhost:8000/docs`

## 💡 最佳实践

1. **开始工作时**：直接运行你的实验脚本，服务器会自动启动
2. **工作期间**：服务器持续运行，支持多个实验脚本
3. **结束工作时**：运行 `python stop_server.py` 清理服务器
4. **调试时**：访问 `http://localhost:8000/docs` 查看API文档

## 🎯 总结

ExpManager现在提供了**完全自动化的用户体验**：
- ✅ 零配置启动：无需手动启动任何服务
- ✅ 智能管理：自动检测和启动必要的服务  
- ✅ 持续运行：服务器独立运行，支持多个实验
- ✅ 用户控制：用户决定何时停止服务器
- ✅ 优雅降级：服务器不可用时仍可正常工作

这就是一个真正专业的Python库应有的体验！🚀

#!/usr/bin/env python3
"""
简单的自动启动测试
测试ExpManager库的自动启动功能
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from expmanager import start_experiment, configure
    print("ExpManager库导入成功")
except ImportError as e:
    print(f"ExpManager库导入失败: {e}")
    sys.exit(1)


def test_auto_start():
    """测试自动启动功能"""
    print("开始测试自动启动功能...")
    
    # 配置自动启动
    configure(
        verbose=True,
        server_auto_start=True,
        auto_open_browser=False  # 测试时不打开浏览器
    )
    
    print("配置完成，开始启动实验...")
    
    # 启动实验 - 这里应该自动启动服务器
    exp_id = start_experiment(
        name="自动启动测试实验",
        hypothesis="ExpManager应该能够自动启动服务器"
    )
    
    print(f"实验已启动，ID: {exp_id}")
    
    # 模拟一些工作
    for i in range(3):
        print(f"执行步骤 {i+1}/3")
        time.sleep(1)
    
    print("实验完成")
    return exp_id


if __name__ == "__main__":
    print("=" * 50)
    print("ExpManager 自动启动测试")
    print("=" * 50)
    
    try:
        exp_id = test_auto_start()
        print(f"测试成功完成，实验ID: {exp_id}")
        print("如果看到这条消息，说明自动启动功能正常工作")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("=" * 50)

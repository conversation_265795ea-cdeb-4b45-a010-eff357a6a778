"""
服务器管理模块
自动启动和管理实验管理服务器
"""

import os
import sys
import time
import subprocess
import requests
import signal
from pathlib import Path
from typing import Optional, Tuple

from .config import Config
from .exceptions import ServerStartupError, ServerNotAvailableError


class ServerManager:
    """服务器管理器"""
    
    def __init__(self, config: Config = None):
        """
        初始化服务器管理器
        
        Args:
            config: 配置对象
        """
        self.config = config or Config()
        self.backend_process = None
        self.frontend_process = None
        
    def is_server_running(self) -> bool:
        """检查服务器是否运行"""
        try:
            response = requests.get(f"{self.config.api_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def start_server(self, force_restart: bool = False) -> bool:
        """
        启动服务器
        
        Args:
            force_restart: 是否强制重启
            
        Returns:
            是否启动成功
        """
        if not force_restart and self.is_server_running():
            if self.config.verbose:
                print("服务器已在运行")
            return True

        try:
            # 查找服务器文件
            server_path = self._find_server_path()
            if not server_path:
                raise ServerStartupError("未找到服务器文件")

            # 启动后端
            self._start_backend(server_path)

            # 启动前端
            self._start_frontend(server_path)

            # 等待服务器启动
            if self._wait_for_server():
                if self.config.verbose:
                    print("服务器启动成功")
                return True
            else:
                raise ServerStartupError("服务器启动超时")

        except Exception as e:
            if self.config.verbose:
                print(f"服务器启动失败: {e}")
            return False
    
    def stop_server(self):
        """停止服务器"""
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=10)
            except:
                self.backend_process.kill()
            self.backend_process = None
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=10)
            except:
                self.frontend_process.kill()
            self.frontend_process = None
        
        if self.config.verbose:
            print("服务器已停止")
    
    def _find_server_path(self) -> Optional[Path]:
        """查找服务器路径"""
        # 可能的服务器位置
        possible_paths = [
            Path(__file__).parent.parent,  # 包内路径
            Path.cwd(),  # 当前目录
            Path.cwd() / "experiment-manager",  # 子目录
            Path.home() / ".expmanager",  # 用户目录
        ]
        
        for path in possible_paths:
            if (path / "backend" / "main.py").exists():
                return path
        
        return None
    
    def _start_backend(self, server_path: Path):
        """启动后端服务"""
        backend_path = server_path / "backend"
        
        # 检查依赖
        requirements_file = backend_path / "requirements.txt"
        if requirements_file.exists():
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
                ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            except:
                pass  # 忽略依赖安装失败
        
        # 启动后端
        cmd = [
            sys.executable, "-m", "uvicorn", "main:app",
            "--host", "0.0.0.0", "--port", "8000"
        ]
        
        self.backend_process = subprocess.Popen(
            cmd,
            cwd=backend_path,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        
        if self.config.verbose:
            print("后端服务启动中...")
    
    def _start_frontend(self, server_path: Path):
        """启动前端服务"""
        frontend_path = server_path / "frontend"

        if not frontend_path.exists():
            if self.config.verbose:
                print("前端目录不存在，跳过前端启动")
            return  # 前端可选

        # 检查Node.js是否可用
        try:
            subprocess.check_call(
                ["node", "--version"],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
        except:
            if self.config.verbose:
                print("Node.js未安装，无法启动前端服务")
            return

        # 检查npm是否可用
        try:
            subprocess.check_call(
                ["npm", "--version"],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
        except:
            if self.config.verbose:
                print("npm未安装，无法启动前端服务")
            return

        # 检查Node.js依赖
        if (frontend_path / "package.json").exists():
            try:
                if self.config.verbose:
                    print("检查前端依赖...")

                # 检查node_modules是否存在
                if not (frontend_path / "node_modules").exists():
                    if self.config.verbose:
                        print("安装前端依赖...")
                    subprocess.check_call(
                        ["npm", "install"],
                        cwd=frontend_path,
                        stdout=subprocess.DEVNULL if not self.config.verbose else None,
                        stderr=subprocess.DEVNULL if not self.config.verbose else None
                    )

                # 启动前端
                if self.config.verbose:
                    print("启动前端服务...")

                self.frontend_process = subprocess.Popen(
                    ["npm", "run", "dev", "--", "--port", "3000"],
                    cwd=frontend_path,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )

                if self.config.verbose:
                    print("前端服务启动中... (http://localhost:3000)")

            except Exception as e:
                if self.config.verbose:
                    print(f"前端启动失败: {e}")
                    print("提示: 请确保Node.js和npm已正确安装")
                    print("或手动启动前端: cd frontend && npm install && npm run dev")
    
    def _wait_for_server(self) -> bool:
        """等待服务器启动"""
        start_time = time.time()
        
        while time.time() - start_time < self.config.server_startup_timeout:
            if self.is_server_running():
                return True
            time.sleep(1)
        
        return False
    
    def __del__(self):
        """析构函数，清理进程"""
        self.stop_server()


# 全局服务器管理器
_server_manager = None


def ensure_server_running(config: Config = None) -> bool:
    """
    确保服务器运行
    
    Args:
        config: 配置对象
        
    Returns:
        是否成功启动
    """
    global _server_manager
    
    if _server_manager is None:
        _server_manager = ServerManager(config)
    
    if not _server_manager.is_server_running():
        if config and config.server_auto_start:
            return _server_manager.start_server()
        else:
            return False
    
    return True


def stop_global_server():
    """停止全局服务器"""
    global _server_manager
    if _server_manager:
        _server_manager.stop_server()
        _server_manager = None


# 注册退出清理
import atexit
atexit.register(stop_global_server)

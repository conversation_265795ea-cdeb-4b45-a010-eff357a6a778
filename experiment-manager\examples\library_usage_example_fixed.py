"""
ExpManager库使用示例（修复版）
演示如何在Python项目中使用expmanager库
"""

import sys
import os
import time
import random

# 添加父目录到Python路径（用于本地开发）
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import numpy as np
    from sklearn.datasets import make_classification
    from sklearn.model_selection import train_test_split
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.linear_model import LogisticRegression
    from sklearn.metrics import accuracy_score
    SKLEARN_AVAILABLE = True
except ImportError:
    print("警告: scikit-learn未安装，将使用模拟数据")
    SKLEARN_AVAILABLE = False

# 导入expmanager库
try:
    from expmanager import start_experiment, configure
    print("ExpManager库导入成功")
except ImportError as e:
    print(f"错误: ExpManager库导入失败: {e}")
    print("解决方案:")
    print("   1. 运行: pip install -e . （在experiment-manager目录下）")
    print("   2. 或者运行: pip install expmanager")
    sys.exit(1)


def setup_environment():
    """配置实验环境"""
    print("配置实验环境...")
    
    # 全局配置
    configure(
        verbose=True,                    # 详细输出
        auto_open_browser=True,          # 自动打开浏览器
        fallback_mode=True,              # 降级模式（服务器不可用时仍可运行）
        server_auto_start=True           # 自动启动服务器
    )
    
    print("环境配置完成")


def experiment_1_model_comparison():
    """实验1: 模型对比实验"""
    
    # 启动实验管理
    exp_id = start_experiment(
        name="随机森林 vs 逻辑回归对比实验",
        hypothesis="在合成数据集上，随机森林的准确率将比逻辑回归高5%以上",
        description="使用sklearn生成的合成分类数据集，对比两种经典算法的性能",
        tags=["model-comparison", "sklearn", "classification"]
    )
    
    print(f"实验1已启动 (ID: {exp_id})")
    
    # 生成合成数据
    print("生成合成数据集...")
    X, y = make_classification(
        n_samples=1000,
        n_features=20,
        n_informative=15,
        n_redundant=5,
        n_classes=2,
        random_state=42
    )
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    results = {}
    
    # 测试随机森林
    print("训练随机森林...")
    rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
    rf_model.fit(X_train, y_train)
    rf_pred = rf_model.predict(X_test)
    rf_accuracy = accuracy_score(y_test, rf_pred)
    results["RandomForest"] = rf_accuracy
    
    print(f"   随机森林准确率: {rf_accuracy:.4f}")
    
    # 测试逻辑回归
    print("训练逻辑回归...")
    lr_model = LogisticRegression(random_state=42, max_iter=1000)
    lr_model.fit(X_train, y_train)
    lr_pred = lr_model.predict(X_test)
    lr_accuracy = accuracy_score(y_test, lr_pred)
    results["LogisticRegression"] = lr_accuracy
    
    print(f"   逻辑回归准确率: {lr_accuracy:.4f}")
    
    # 分析结果
    best_model = max(results, key=results.get)
    accuracy_diff = abs(results["RandomForest"] - results["LogisticRegression"])
    
    print(f"\n实验结果:")
    print(f"   最佳模型: {best_model}")
    print(f"   准确率差异: {accuracy_diff:.4f}")
    
    if best_model == "RandomForest" and accuracy_diff >= 0.05:
        print("假设验证成功：随机森林确实比逻辑回归准确率高5%以上")
    else:
        print("假设验证失败：结果与预期不符")
    
    print(f"\n实验1完成！")
    # 脚本结束时会自动打开复盘页面


def main():
    """主函数 - 运行实验"""
    print("=" * 60)
    print("ExpManager库使用示例")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    try:
        # 运行实验1
        print("\n" + "=" * 40)
        experiment_1_model_comparison()
        
        # 等待一下，让用户看到结果
        print("\n等待5秒后自动打开复盘页面...")
        time.sleep(5)
        
    except KeyboardInterrupt:
        print("\n警告: 实验被用户中断")
    except Exception as e:
        print(f"\n错误: 实验执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("实验完成！")
    print("复盘页面将自动打开，请查看详细分析")
    print("=" * 60)


if __name__ == "__main__":
    main()

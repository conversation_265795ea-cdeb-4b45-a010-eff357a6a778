#!/usr/bin/env python3
"""
简单的library测试脚本
测试expmanager库的基本功能
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from expmanager import start_experiment
    print("ExpManager库导入成功")
except ImportError as e:
    print(f"ExpManager库导入失败: {e}")
    sys.exit(1)


def simple_test():
    """简单测试"""
    print("开始测试...")
    
    # 启动实验
    exp_id = start_experiment(
        name="简单测试实验",
        hypothesis="这是一个测试假设"
    )
    
    print(f"实验已启动，ID: {exp_id}")
    
    # 模拟一些工作
    for i in range(3):
        print(f"执行步骤 {i+1}/3")
        time.sleep(1)
    
    print("实验完成")
    return exp_id


if __name__ == "__main__":
    print("=" * 50)
    print("ExpManager Library 测试")
    print("=" * 50)
    
    try:
        exp_id = simple_test()
        print(f"测试成功完成，实验ID: {exp_id}")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("=" * 50)

#!/usr/bin/env python3
"""
停止ExpManager服务器
用户可以运行此脚本来手动停止服务器
"""

import subprocess
import sys
import time


def stop_servers():
    """停止所有ExpManager相关服务器"""
    print("正在停止ExpManager服务器...")
    
    stopped_any = False
    
    # 停止端口8000上的进程（后端）
    try:
        if sys.platform == "win32":
            # Windows
            result = subprocess.run([
                "netstat", "-ano"
            ], capture_output=True, text=True)
            
            lines = result.stdout.split('\n')
            for line in lines:
                if ':8000' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        try:
                            subprocess.run([
                                "taskkill", "/F", "/PID", pid
                            ], check=True, capture_output=True)
                            print(f"已停止后端服务 (PID: {pid})")
                            stopped_any = True
                        except:
                            pass
        else:
            # Unix/Linux
            try:
                result = subprocess.run([
                    "lsof", "-ti:8000"
                ], capture_output=True, text=True)
                
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid:
                        try:
                            subprocess.run([
                                "kill", "-9", pid
                            ], check=True)
                            print(f"已停止后端服务 (PID: {pid})")
                            stopped_any = True
                        except:
                            pass
            except:
                pass
                
    except Exception as e:
        print(f"停止后端服务时出错: {e}")
    
    # 停止端口3000上的进程（前端）
    try:
        if sys.platform == "win32":
            # Windows
            result = subprocess.run([
                "netstat", "-ano"
            ], capture_output=True, text=True)
            
            lines = result.stdout.split('\n')
            for line in lines:
                if ':3000' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        try:
                            subprocess.run([
                                "taskkill", "/F", "/PID", pid
                            ], check=True, capture_output=True)
                            print(f"已停止前端服务 (PID: {pid})")
                            stopped_any = True
                        except:
                            pass
        else:
            # Unix/Linux
            try:
                result = subprocess.run([
                    "lsof", "-ti:3000"
                ], capture_output=True, text=True)
                
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid:
                        try:
                            subprocess.run([
                                "kill", "-9", pid
                            ], check=True)
                            print(f"已停止前端服务 (PID: {pid})")
                            stopped_any = True
                        except:
                            pass
            except:
                pass
                
    except Exception as e:
        print(f"停止前端服务时出错: {e}")
    
    if stopped_any:
        print("ExpManager服务器已停止")
    else:
        print("没有发现运行中的ExpManager服务器")


if __name__ == "__main__":
    print("=" * 50)
    print("ExpManager 服务器停止工具")
    print("=" * 50)
    
    try:
        stop_servers()
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"停止服务器时发生错误: {e}")
    
    print("=" * 50)
